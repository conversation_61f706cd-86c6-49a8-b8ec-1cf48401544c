// SPDX-License-Identifier: GPL-2.0
/*
 * AM62x LP SK: https://www.ti.com/tool/SK-AM62-LP
 *
 * Copyright (C) 2021-2023 Texas Instruments Incorporated - https://www.ti.com/
 */

/dts-v1/;

#include <dt-bindings/input/input.h>
#include <dt-bindings/gpio/gpio.h>
#include "victel-am62-common.dtsi"
#include "p3-v1.1.dtsi"

/ {
    compatible = "ti,am62-lp-sk", "ti,am625";
    model = "Victel AM62X Platform By LiMing";

    /*described regulators*/
    vbat: regulator-0 {
        compatible = "regulator-fixed";
        regulator-name = "vbat";
        regulator-min-microvolt = <7200000>;//ak809 7.2 input voltage
        regulator-max-microvolt = <7200000>;
        regulator-always-on;
        regulator-boot-on;
    };

    vdd_3v3:regulator-1 {
        compatible = "regulator-fixed";
        regulator-name = "vdd_3v3";
        regulator-min-microvolt = <3300000>;
        regulator-max-microvolt = <3300000>;
        vin-supply = <&vdd_5v>;
        regulator-always-on;
        regulator-boot-on;        
    };

    vdd_1v1:regulator-2 {
        compatible = "regulator-fixed";
        regulator-name = "vdd_1v1";
        regulator-min-microvolt = <1100000>;
        regulator-max-microvolt = <1100000>;
        vin-supply = <&vdd_5v>;
        regulator-always-on;
        regulator-boot-on;        
    };

    vdd_1v8:regulator-3 {
        compatible = "regulator-fixed";
        regulator-name = "vdd_1v8";
        regulator-min-microvolt = <1800000>;
        regulator-max-microvolt = <1800000>;
        vin-supply = <&vdd_5v>;
        regulator-always-on;
        regulator-boot-on;        
    };   
    vdd_5v:regulator-4 {
        compatible = "regulator-fixed";
        regulator-name = "vdd_5v";
        regulator-min-microvolt = <5000000>;
        regulator-max-microvolt = <5000000>;
        vin-supply = <&vbat>;
        regulator-always-on;
        regulator-boot-on;        
    };

    vdd_arm_0P85V:regulator-5 {
        compatible = "regulator-fixed";
        regulator-name = "vdd_arm_0P85V";
        regulator-min-microvolt = <850000>;
        regulator-max-microvolt = <850000>;
        vin-supply = <&vbat>;
        regulator-always-on;
        regulator-boot-on;        
    };

    vcc_3V0A:regulator-6 {
        compatible = "regulator-fixed";
        regulator-name = "vcc_3V0A";
        regulator-min-microvolt = <3000000>;
        regulator-max-microvolt = <3000000>;
        vin-supply = <&vdd_3v3>;
        regulator-always-on;
        regulator-boot-on;        
    };   

    tcxo_3v0:regulator-7 {
        compatible = "regulator-fixed";
        regulator-name = "tcxo_3v0";
        regulator-min-microvolt = <3000000>;
        regulator-max-microvolt = <3000000>;
        vin-supply = <&vdd_3v3>;
        regulator-always-on;
        regulator-boot-on;        
    };     

    rt_5v0:regulator-8 {
        compatible = "regulator-fixed";
        regulator-name = "rt_5v0";
        regulator-min-microvolt = <5000000>;
        regulator-max-microvolt = <5000000>;
        vin-supply = <&vdd_5v>;
        regulator-always-on;
        regulator-boot-on;        
    }; 

   leds:leds {
        status = "okay";
        compatible = "gpio-leds";
/*
        LED-G {
                label = "led-g";
                gpios = <&main_gpio0 67 GPIO_ACTIVE_HIGH>;
                linux,default-trigger = "none";
                function = LED_FUNCTION_HEARTBEAT;
                default-state = "off";
        };

        LED-R {
            label = "led-r";
            gpios = <&main_gpio0 42 GPIO_ACTIVE_HIGH>;
            linux,default-trigger = "none";
            function = LED_FUNCTION_HEARTBEAT;
            default-state = "off";
        };    
*/
        SD-PWR {
            label = "sd-pwr";
            gpios = <&main_gpio0 63 GPIO_ACTIVE_LOW>;
            default-state = "off";
        };

        KEY_LIGHT {
            status = "okay";
            label = "key_light";
            gpios = <&main_gpio1 38 GPIO_ACTIVE_HIGH>;
            default-state = "on";
        };

        BL_ON {
            label = "bl_on";
            gpios = <&main_gpio1 9 GPIO_ACTIVE_HIGH>;
            default-state = "on";
        };

        GPS_ON {
            label = "gps_on";
            gpios = <&main_gpio0 36 GPIO_ACTIVE_LOW>;
            default-state = "off";
        };

        BT_ON {
            label = "bt_on";
            gpios = <&main_gpio0 37 GPIO_ACTIVE_HIGH>;
            default-state = "off";
        };

    };

    rfctrl: rf-ctrl {
        status = "okay";
        compatible = "gpio-leds";

        RF_PWN {
            label = "rf_power";
            gpios = <&mcu_gpio0 17 GPIO_ACTIVE_HIGH>;
            linux,default-trigger = "none";
            function = LED_FUNCTION_STATUS;
            default-state = "off";
        };
        TX_ON {
            label = "tx_on";
            gpios = <&mcu_gpio0 18 GPIO_ACTIVE_HIGH>;
            linux,default-trigger = "none";
            function = LED_FUNCTION_STATUS;
            default-state = "off";
        };
        RX_ON {
            label = "rx_on";
            gpios = <&mcu_gpio0 19 GPIO_ACTIVE_HIGH>;
            linux,default-trigger = "none";
            function = LED_FUNCTION_STATUS;
            default-state = "off";
        };

        RX_ON_2 {
            label = "rx_on_2";
            gpios = <&main_gpio0 76 GPIO_ACTIVE_HIGH>;
            linux,default-trigger = "none";
            function = LED_FUNCTION_STATUS;            
            default-state = "off";
        };  

        RX_VCO_ON {
            label = "rx_vco_on";
            gpios = <&mcu_gpio0 20 GPIO_ACTIVE_HIGH>;
            linux,default-trigger = "none";
            function = LED_FUNCTION_STATUS;
            default-state = "off";
        };

        RX_VCO_ON_2 {
            label = "rx_vco_on_2";
            gpios = <&main_gpio0 75 GPIO_ACTIVE_HIGH>;
            linux,default-trigger = "none";
            function = LED_FUNCTION_STATUS;
            default-state = "off";
        };

        PS_APC {
            label = "ps_apc";
            gpios = <&mcu_gpio0 23 GPIO_ACTIVE_HIGH>;
            linux,default-trigger = "none";
            function = LED_FUNCTION_STATUS;
            default-state = "off";
        };        
    };

    /* GPIO按钮配置 - 电源按钮和电源控制 */
/*
    gpio_keys: gpio-keys {
        compatible = "gpio-keys";

        power_button: power-button {
            label = "Power Button";
            linux,code = <KEY_POWER>;
            gpios = <&main_gpio0 61 GPIO_ACTIVE_HIGH>;
        };
    };
*/

    /* GPIO电源控制输出 */
/*
    gpio_power_ctrl: gpio-power-ctrl {
        compatible = "gpio-leds";

        power_off_ctrl: power-off-ctrl {
            label = "power-off-ctrl";
            gpios = <&main_gpio0 60 GPIO_ACTIVE_LOW>;
            default-state = "keep";
        };
    };
*/
};


&cpsw_port2 {
    status = "disabled";
};

&ospi0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&qspi0flash_pins_default>;

    flash@0{
        compatible = "is25lp512", "jedec,spi-nor";
        reg = <0x0>;
        spi-tx-bus-width = <1>;
        spi-rx-bus-width = <4>;
        spi-max-frequency = <25000000>;
        cdns,tshsl-ns = <60>;
        cdns,tsd2d-ns = <60>;
        cdns,tchsh-ns = <60>;
        cdns,tslch-ns = <60>;
        cdns,read-delay = <4>;
        cdns,phy-mode;

        partitions {
            compatible = "fixed-partitions";
            #address-cells = <1>;
            #size-cells = <1>;

            partition@0 {
                label = "ospi.tiboot3";
                reg = <0x0 0x80000>;
            };

            partition@80000 {
                label = "ospi.tispl";
                reg = <0x80000 0x100000>;
            };

            partition@180000 {
                label = "ospi.u-boot";
                reg = <0x180000 0x100000>;
            };

            partition@280000 {
                label = "ospi.dtb";
                reg = <0x280000 0x20000>;
            };
            
            partition@2a0000 {
                label = "ospi.env.backup";
                reg = <0x2a0000 0x20000>;
            };   

            partition@2c0000 {
                label = "ospi.calibration";
                reg = <0x2c0000 0x40000>;
            };

            partition@300000 {
                label = "ospi.kernel";
                reg = <0x300000 0x800000>;
            };

            partition@b00000 {
                label = "rootfs";
                reg = <0xb00000 0x1500000>;
            };

            partition@2000000 {
                label = "victel";
                reg = <0x2000000 0x1fc0000>;
            };
            /* 16MB --  fc0000
                * 32MB -- 1fc0000
                * 64MB -- 3fc0000
                */
            partition@3fc0000 {
                label = "ospi.phypattern";
                reg = <0x3fc0000 0x40000>;
            };
        };
    };
};

&main_spi0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi0lcdadc_pins_default>;
    // dmas = <&main_pktdma 0xc300 0>, <&main_pktdma 0x4300 0>;
    // dma-names = "tx0", "rx0";
    ti,spi-num-cs = <4>;
    ti,pindir-d0-out-d1-in;

    panel: display@0 {
            compatible = "victel,gc9307-uboot", "jianghehai,xhzy1713268za1";
            spi-max-frequency = <50000000>;
            reg = <0>;
            width = <320>;
            height = <240>;
            buswidth = <8>;
            rotate = <0>;
            reset-gpio = <&main_gpio0 32 GPIO_ACTIVE_LOW>; 
            dc-gpio = <&main_gpio0 35 GPIO_ACTIVE_HIGH>;
            cs-gpio = <&main_gpio1 15 GPIO_ACTIVE_LOW>;
            led-gpio = <&main_gpio1 9 GPIO_ACTIVE_HIGH>;
    };
};

/*统一地方对GPIO管脚进行复用*/
&main_gpio0 {
    pinctrl-names = "default";
    pinctrl-0 = <&gpio0_pins_default>;
    gpio-line-names = 
        "", "", "", "", "",   /* 0 - 4*/
        "", "", "", "", "",   /* 5 - 9*/
        "", "", "", "", "",   /* 10 - 14*/
        "", "", "", "", "",   /* 15 - 19*/
        "", "", "", "", "",   /* 20 - 24*/
        "", "", "", "", "",   /* 25 - 29*/
        "", "", "", "", "",   /* 30 - 34*/
        "", "GPS_ON", "BT_ON_N", "", "BT_STATE",   /* 35 - 39*/
        "", "", "LED_R", "", "",   /* 40 - 44*/
        "KEY_R0", "KEY_R1", "KEY_R2", "KEY_R3", "KEY_R4",   /* 45 - 49*/
        "KEY_C0", "KEY_C1", "KEY_C2", "KEY_C3", "KEY_C4",   /* 50 - 54*/
        "", "", "AUDIO_PA_ON", "", "CODEC2_RST_N",   /* 55 - 59*/
        "PWR_OFF", "KEY_MIDDLE", "LCD_RS", "SD_EN_N", "NFC_PD",   /* 60 - 64*/
        "", "", "LED_G", "", "",   /* 65 - 69*/
        "", "", "", "SIM_CLK", "SIM_IO",   /* 70 - 74*/
        "IO1_JM", "IO2_JM", "IO3_JM", "IO4_JM", "JM_EN_N",   /* 75 - 79*/
        "SIM_EN_N", "", "", "", "",   /* 80 - 84*/
        "PTT_N", "ALARM_N", "", "", "",   /* 85 - 89*/
        "", "";              /* 90 - 91*/
};

&main_gpio1 {
    pinctrl-names = "default";
    pinctrl-0 = <&gpio1_pins_default>;
    gpio-line-names = 
        "", "ENC_S", "", "ENC_A", "",   /* 0 - 4*/
        "", "ENC_B", "ID", "", "LCD_LIGHT",   /* 5 - 9*/
        "", "", "", "", "",   /* 10 - 14*/
        "", "", "", "", "",   /* 15 - 19*/
        "", "", "", "", "",   /* 20 - 24*/
        "", "", "", "", "",   /* 25 - 29*/
        "", "", "", "", "",   /* 30 - 34*/
        "", "", "", "KEY_LIGHT", "GP1_39",   /* 35 - 39*/
        "", "", "", "", "",   /* 40 - 44*/
        "", "", "", "", "",   /* 45 - 49*/
        "", "";               /* 50 - 51*/
};

&mcu_gpio0 {
    pinctrl-names = "default";
    pinctrl-0 = <&mcugpio0_pins_default>;
    gpio-line-names = 
        "", "", "", "", "",   /* 0 - 4*/
        "", "", "", "", "",   /* 5 - 9*/
        "", "", "", "", "",   /* 10 - 14*/
        "", "", "RF_PWR_EN", "TX_ON", "RX_ON",   /* 15 - 19*/
        "RX_VCO_ON", "", "RX_PLL_LD", "PS_APC";      /* 20 - 23*/
};


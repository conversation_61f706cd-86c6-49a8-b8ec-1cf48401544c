#include <common.h>
#include <asm/gpio.h>
#include <k3-ddrss.h>
#include <asm/io.h>
#include <k3_gpio.h>

static inline uint32_t read_reg(void __iomem *base, uint32_t offset)
{
    return readl(base + offset);
}

static inline void write_reg(void __iomem *base, uint32_t offset, uint32_t value)
{
    writel(value, base + offset);
}

/* 位域操作函数 - 替代CSL_FEXTR和CSL_FINSR */
static inline uint32_t extract_field(uint32_t reg, uint32_t msb, uint32_t lsb)
{
    return ((reg) >> (lsb)) & ((((uint32_t)1U) << ((msb) - (lsb) + ((uint32_t)1U))) - ((uint32_t)1U));
}

static inline uint32_t insert_field(uint32_t reg, uint32_t msb, uint32_t lsb, uint32_t val)
{
    uint32_t mask = ((((uint32_t)1U) << ((msb) - (lsb) + ((uint32_t)1U))) - ((uint32_t)1U)) << (lsb);
    return (reg & (~mask)) | (((val) << (lsb)) & mask);
}

int am62x_gpio_set_direction(void __iomem *base, uint32_t pin_num, uint32_t pin_dir)
{
    uint32_t reg_val;
    unsigned long flags;
    
    uint32_t reg_index = GPIO_GET_REG_INDEX(pin_num);
    uint32_t bit_pos = GPIO_GET_BIT_POS(pin_num);
    
    reg_val = read_reg(base, GPIO_DIR(reg_index));
    reg_val = insert_field(reg_val, bit_pos, bit_pos, pin_dir & 0x01U);
    write_reg(base, GPIO_DIR(reg_index), reg_val);
        
    return 0;
}

int am62x_gpio_get_value(void __iomem *base, uint32_t pin_num)
{
    uint32_t in_data;
    
    uint32_t reg_index = GPIO_GET_REG_INDEX(pin_num);
    uint32_t bit_pos = GPIO_GET_BIT_POS(pin_num);
    
    in_data = read_reg(base, GPIO_IN_DATA(reg_index));
    in_data = extract_field(in_data, bit_pos, bit_pos);
         
    return (int)in_data;
}

void am62x_gpio_set_value(void __iomem *base, uint32_t pin_num, int value)
{
    unsigned long flags;
    
    uint32_t reg_index = GPIO_GET_REG_INDEX(pin_num);
    uint32_t reg_val = GPIO_GET_BIT_MASK(pin_num);
    
    if (value) {
        write_reg(base, GPIO_SET_DATA(reg_index), reg_val);
    } else {
        write_reg(base, GPIO_CLR_DATA(reg_index), reg_val);
    }
        
}
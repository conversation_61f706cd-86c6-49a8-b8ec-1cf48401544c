/*
 * AM62x GPIO Driver Header - Linux Kernel Implementation
 * 
 * 定义AM62x GPIO驱动的数据结构、常量和函数声明
 */

#ifndef __AM62X_GPIO_LINUX_H__
#define __AM62X_GPIO_LINUX_H__

/* AM62x GPIO寄存器偏移定义 */
/* GPIO v0 寄存器偏移 (主要GPIO控制器) */
#define GPIO_PID                    0x00
#define GPIO_PCR                    0x04
#define GPIO_BINTEN                 0x08
#define GPIO_DIR(n)                 (0x10 + ((n) * 0x28))
#define GPIO_OUT_DATA(n)            (0x14 + ((n) * 0x28))
#define GPIO_SET_DATA(n)            (0x18 + ((n) * 0x28))
#define GPIO_CLR_DATA(n)            (0x1C + ((n) * 0x28))
#define GPIO_IN_DATA(n)             (0x20 + ((n) * 0x28))
#define GPIO_SET_RIS_TRIG(n)        (0x24 + ((n) * 0x28))
#define GPIO_SET_FAL_TRIG(n)        (0x28 + ((n) * 0x28))
#define GPIO_INTSTAT(n)             (0x2C + ((n) * 0x28))

/* GPIO方向定义 */
#define GPIO_DIRECTION_OUTPUT       0x0
#define GPIO_DIRECTION_INPUT        0x1

/* GPIO触发类型定义 */
#define GPIO_TRIG_TYPE_NONE         0x0
#define GPIO_TRIG_TYPE_RISING       0x1
#define GPIO_TRIG_TYPE_FALLING      0x2
#define GPIO_TRIG_TYPE_BOTH         0x3

/* GPIO引脚计算宏 */
#define GPIO_PINS_PER_REG_SHIFT     5U
#define GPIO_PINS_PER_BANK_SHIFT    4U
#define GPIO_MAX_PINS_PER_PORT      8U
#define GPIO_MAX_PORT               8U
#define GPIO_MAX_PIN_PER_BANK       16U

/* v0版本宏 */
#define GPIO_GET_BANK_INDEX(pinNum)     (((uint32_t) pinNum) >> GPIO_PINS_PER_BANK_SHIFT)
#define GPIO_GET_REG_INDEX(pinNum)      (((uint32_t) pinNum) >> GPIO_PINS_PER_REG_SHIFT)
#define GPIO_GET_BIT_POS(pinNum)        (pinNum - ((GPIO_GET_REG_INDEX(pinNum)) << GPIO_PINS_PER_REG_SHIFT))
#define GPIO_GET_BIT_MASK(pinNum)       (((uint32_t) 1U) << GPIO_GET_BIT_POS(pinNum))

/* AM62x GPIO基地址定义 */
#define AM62X_GPIO0_BASE            0x00600000UL
#define AM62X_GPIO1_BASE            0x00601000UL

/* AM62x GPIO引脚定义 */
#define POWER_BUTTON_GPIO		61  /* GPIO0_61 */
#define POWER_OFF_GPIO			60  /* GPIO0_60 */
#define POWER_GREEN_LED_GPIO	67  /* GPIO0_42 */

/* GPIO控制器类型 */
enum am62x_gpio_type {
    AM62X_GPIO_TYPE_V0,     /* 主域GPIO */
    AM62X_GPIO_TYPE_V1,     /* MCU域GPIO */
};

/* GPIO中断类型 */
enum am62x_gpio_irq_type {
    AM62X_GPIO_IRQ_LEVEL_LOW,
    AM62X_GPIO_IRQ_LEVEL_HIGH,
    AM62X_GPIO_IRQ_EDGE_FALLING,
    AM62X_GPIO_IRQ_EDGE_RISING,
    AM62X_GPIO_IRQ_EDGE_BOTH,
};

/* GPIO控制器结构体 */
struct am62x_gpio_chip {
    void __iomem *base;
    enum am62x_gpio_type type;
};

/* GPIO配置结构体 */
struct am62x_gpio_config {
    u32 pin;
    u32 direction;
    u32 value;
    u32 pull_type;
    u32 drive_strength;
};

/* 寄存器读写内联函数 */
static inline uint32_t am62x_gpio_read_reg(void __iomem *base, uint32_t offset)
{
    return readl(base + offset);
}

static inline void am62x_gpio_write_reg(void __iomem *base, uint32_t offset, uint32_t value)
{
    writel(value, base + offset);
}

/* 位域操作内联函数 */
static inline uint32_t am62x_gpio_extract_field(uint32_t reg, uint32_t msb, uint32_t lsb)
{
    return ((reg) >> (lsb)) & ((((uint32_t)1U) << ((msb) - (lsb) + ((uint32_t)1U))) - ((uint32_t)1U));
}

static inline uint32_t am62x_gpio_insert_field(uint32_t reg, uint32_t msb, uint32_t lsb, uint32_t val)
{
    uint32_t mask = ((((uint32_t)1U) << ((msb) - (lsb) + ((uint32_t)1U))) - ((uint32_t)1U)) << (lsb);
    return (reg & (~mask)) | (((val) << (lsb)) & mask);
}

/* 函数声明 */
int am62x_gpio_set_direction(void __iomem *base, uint32_t pin_num, uint32_t pin_dir);
int am62x_gpio_get_value(void __iomem *base, uint32_t pin_num);
void am62x_gpio_set_value(void __iomem *base, uint32_t pin_num, int value);

/* 设备树兼容性字符串 */
#define AM62X_GPIO_COMPAT_V0        "ti,am62x-gpio"
#define AM62X_GPIO_COMPAT_V1        "ti,am62x-mcu-gpio"

#endif /* __AM62X_GPIO_LINUX_H__ */
